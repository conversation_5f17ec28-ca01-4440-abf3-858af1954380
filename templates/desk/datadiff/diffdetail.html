{{ define "desk/datadiff/diffdetail.html"}}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>接口Diff详情分析 - fwyytool</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome 6 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    <!-- ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <!-- Moment.js -->
    <script src="https://cdn.jsdelivr.net/npm/moment@2.29.4/moment.min.js"></script>
    
    <style>
        :root {
            /* Apple Design System Colors */
            --apple-blue: #007AFF;
            --apple-blue-light: #5AC8FA;
            --apple-blue-dark: #0051D5;
            --apple-green: #34C759;
            --apple-orange: #FF9500;
            --apple-red: #FF3B30;
            --apple-purple: #AF52DE;
            --apple-pink: #FF2D92;
            --apple-yellow: #FFCC00;
            
            /* Neutral Colors - Apple Style */
            --apple-gray-1: #8E8E93;
            --apple-gray-2: #AEAEB2;
            --apple-gray-3: #C7C7CC;
            --apple-gray-4: #D1D1D6;
            --apple-gray-5: #E5E5EA;
            --apple-gray-6: #F2F2F7;
            
            /* Text Colors */
            --text-primary: #1D1D1F;
            --text-secondary: #86868B;
            --text-tertiary: #AEAEB2;
            --text-link: var(--apple-blue);
            
            /* Background Colors */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F5F5F7;
            --bg-tertiary: var(--apple-gray-6);
            --bg-elevated: #FFFFFF;
            
            /* Apple Spacing System */
            --spacing-2xs: 2px;
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 12px;
            --spacing-lg: 16px;
            --spacing-xl: 20px;
            --spacing-2xl: 24px;
            --spacing-3xl: 32px;
            --spacing-4xl: 40px;
            --spacing-5xl: 48px;
            
            /* Apple Border Radius */
            --radius-xs: 4px;
            --radius-sm: 6px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            --radius-2xl: 20px;
            
            /* Apple Shadows */
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04);
            --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
            --shadow-xl: 0 16px 40px rgba(0, 0, 0, 0.16);
            
            /* Apple Typography Scale */
            --font-size-xs: 11px;
            --font-size-sm: 13px;
            --font-size-base: 15px;
            --font-size-lg: 17px;
            --font-size-xl: 19px;
            --font-size-2xl: 22px;
            --font-size-3xl: 28px;
            --font-size-4xl: 34px;
            
            /* Font Weights - Apple Style */
            --font-weight-regular: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
        }
        
        body {
            background: var(--bg-secondary);
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
            color: var(--text-primary);
            font-size: var(--font-size-base);
            line-height: 1.47;
            letter-spacing: -0.022em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        /* Apple Typography Hierarchy */
        h1, .h1 { 
            font-size: var(--font-size-4xl); 
            font-weight: var(--font-weight-bold); 
            line-height: 1.12; 
            letter-spacing: -0.025em;
            margin-bottom: var(--spacing-lg);
        }
        h2, .h2 { 
            font-size: var(--font-size-3xl); 
            font-weight: var(--font-weight-semibold); 
            line-height: 1.18; 
            letter-spacing: -0.022em;
            margin-bottom: var(--spacing-md);
        }
        h3, .h3 { 
            font-size: var(--font-size-2xl); 
            font-weight: var(--font-weight-semibold); 
            line-height: 1.27; 
            letter-spacing: -0.019em;
            margin-bottom: var(--spacing-md);
        }
        h4, .h4 { 
            font-size: var(--font-size-xl); 
            font-weight: var(--font-weight-medium); 
            line-height: 1.32; 
            letter-spacing: -0.016em;
            margin-bottom: var(--spacing-sm);
        }
        h5, .h5 { 
            font-size: var(--font-size-lg); 
            font-weight: var(--font-weight-medium); 
            line-height: 1.35; 
            letter-spacing: -0.013em;
            margin-bottom: var(--spacing-sm);
        }
        h6, .h6 { 
            font-size: var(--font-size-base); 
            font-weight: var(--font-weight-semibold); 
            line-height: 1.4; 
            letter-spacing: -0.01em;
            margin-bottom: var(--spacing-xs);
        }
        
        /* Apple Text Styles */
        .text-large { font-size: var(--font-size-xl); line-height: 1.32; }
        .text-body { font-size: var(--font-size-base); line-height: 1.47; }
        .text-caption { font-size: var(--font-size-sm); line-height: 1.38; color: var(--text-secondary); }
        .text-footnote { font-size: var(--font-size-xs); line-height: 1.36; color: var(--text-tertiary); }
        
        .page-header {
            background: linear-gradient(180deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            padding: var(--spacing-3xl) 0 var(--spacing-2xl) 0;
            margin-bottom: var(--spacing-2xl);
            border-bottom: 1px solid var(--apple-gray-5);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }
        
        .page-header .container-fluid {
            position: relative;
        }
        
        .page-title {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
            letter-spacing: -0.025em;
        }
        
        .page-subtitle {
            font-size: var(--font-size-lg);
            font-weight: var(--font-weight-regular);
            color: var(--text-secondary);
            margin-bottom: 0;
            letter-spacing: -0.016em;
        }
        
        .metric-card {
            background: var(--bg-elevated);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            padding: 0;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border: 1px solid var(--apple-gray-5);
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
            height: 120px;
            display: flex;
            flex-direction: column;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            transition: all 0.3s ease;
        }
        
        .metric-card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }
        
        .metric-card-content {
            padding: var(--spacing-xl) var(--spacing-lg);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex: 1;
        }
        
        .metric-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }
        
        .metric-body {
            display: flex;
            justify-content: center;
            align-items: center;
            flex: 1;
        }
        
        /* 不同类型卡片的顶部装饰条颜色 */
        .metric-card.card-info::before {
            background: linear-gradient(90deg, #007AFF 0%, #5AC8FA 100%);
        }
        
        .metric-card.card-danger::before {
            background: linear-gradient(90deg, #FF3B30 0%, #FF6B6B 100%);
        }
        
        .metric-card.card-success::before {
            background: linear-gradient(90deg, #34C759 0%, #30D158 100%);
        }
        
        .metric-card.card-warning::before {
            background: linear-gradient(90deg, #FF9500 0%, #FFCC00 100%);
        }
        
        .metric-icon {
            font-size: var(--font-size-lg);
            opacity: 0.8;
        }
        
        .metric-value {
            font-size: var(--font-size-4xl);
            font-weight: var(--font-weight-bold);
            letter-spacing: -0.025em;
            color: var(--text-primary);
        }
        
        .metric-label {
            color: var(--text-secondary);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            letter-spacing: -0.01em;
            margin: 0;
        }
        
        /* 图标颜色与装饰条保持一致 */
        .metric-card.card-info .metric-icon { color: var(--apple-blue); }
        .metric-card.card-danger .metric-icon { color: var(--apple-red); }
        .metric-card.card-success .metric-icon { color: var(--apple-green); }
        .metric-card.card-warning .metric-icon { color: var(--apple-orange); }
        
        .query-panel {
            background: white;
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-card);
            margin-bottom: var(--spacing-lg);
            overflow: hidden;
        }
        
        .panel-header {
            background: #f8f9fa;
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .panel-body {
            padding: var(--spacing-lg);
        }
        
        .enhanced-datatable {
            background: var(--bg-primary);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-card);
            overflow: hidden;
            border: 1px solid var(--border-color);
        }
        
        .table-controls {
            background: var(--bg-secondary);
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }
        
        .table-controls h5 {
            color: var(--text-primary);
            font-weight: 600;
            margin: 0;
        }
        
        .table-controls .badge {
            background: var(--info-color);
            color: white;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius-sm);
        }
        
        .status-badge {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-xl);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-semibold);
            letter-spacing: -0.01em;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
        }
        
        .status-0 { 
            background: rgba(255, 149, 0, 0.1);
            color: var(--apple-orange); 
            border: 1px solid rgba(255, 149, 0, 0.2);
        }
        .status-1 { 
            background: rgba(52, 199, 89, 0.1);
            color: var(--apple-green); 
            border: 1px solid rgba(52, 199, 89, 0.2);
        }
        .status-2 { 
            background: rgba(255, 59, 48, 0.1);
            color: var(--apple-red); 
            border: 1px solid rgba(255, 59, 48, 0.2);
        }
        
        .diff-badge {
            padding: 0.25rem 0.5rem;
            border-radius: var(--border-radius-sm);
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .diff-has { background-color: #f8d7da; color: #721c24; }
        .diff-none { background-color: #d4edda; color: #155724; }
        
        .params-preview {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            cursor: pointer;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
        }
        
        .action-buttons {
            display: flex;
            gap: var(--spacing-xs);
        }
        
        .stats-summary {
            background: var(--bg-elevated);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            padding: var(--spacing-2xl);
            margin-bottom: var(--spacing-2xl);
            border: 1px solid var(--apple-gray-5);
        }
        
        .time-filter-compact {
            background: rgba(255, 255, 255, 0.8);
            border-radius: var(--radius-lg);
            padding: var(--spacing-sm);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--apple-gray-5);
            box-shadow: var(--shadow-sm);
        }
        
        .time-filter-compact .form-select,
        .time-filter-compact .form-control {
            background: var(--bg-primary);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            color: var(--text-primary);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-regular);
            padding: var(--spacing-xs) var(--spacing-sm);
            transition: all 0.2s ease;
        }
        
        .time-filter-compact .form-select:focus,
        .time-filter-compact .form-control:focus {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
            outline: none;
        }
        
        .time-filter-compact .btn {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
        }
        
        .table-controls .form-select-sm,
        .table-controls .form-control-sm {
            font-size: var(--font-size-sm);
            border-radius: var(--radius-sm);
            border: 1px solid var(--apple-gray-4);
            padding: var(--spacing-xs) var(--spacing-sm);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            transition: all 0.2s ease;
        }
        
        .table-controls .form-select-sm:focus,
        .table-controls .form-control-sm:focus {
            border-color: var(--apple-blue);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
            outline: none;
        }
        
        .table-controls .form-select-sm option {
            padding: var(--spacing-xs);
            color: var(--text-primary);
            background-color: var(--bg-primary);
        }
        
        /* DataTables 分页控件样式优化 */
        .dataTables_wrapper {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', 'Helvetica Neue', Arial, sans-serif;
        }
        
        .dataTables_length {
            margin-bottom: var(--spacing-sm);
        }
        
        .dataTables_length label {
            font-size: var(--font-size-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .dataTables_length select {
            font-size: var(--font-size-sm);
            padding: var(--spacing-2xs) var(--spacing-xs);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            background-color: var(--bg-primary);
            color: var(--text-primary);
            margin: 0 var(--spacing-xs);
            min-width: 60px;
        }
        
        .dataTables_info {
            font-size: var(--font-size-sm);
            color: var(--text-secondary);
            padding: var(--spacing-2xs) 0;
            font-weight: var(--font-weight-regular);
        }
        
        .dataTables_paginate {
            padding: var(--spacing-2xs) 0;
        }
        
        .dataTables_paginate .paginate_button {
            font-size: var(--font-size-xs);
            padding: var(--spacing-xs) var(--spacing-xs);
            margin: 0 var(--spacing-2xs);
            border: 1px solid var(--apple-gray-4);
            border-radius: var(--radius-sm);
            background: var(--bg-primary);
            color: var(--text-primary);
            text-decoration: none;
            transition: all 0.2s ease;
            font-weight: var(--font-weight-medium);
        }
        
        .dataTables_paginate .paginate_button:hover {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-3);
            color: var(--text-primary);
            transform: translateY(-1px);
        }
        
        .dataTables_paginate .paginate_button.current {
            background: var(--apple-blue);
            border-color: var(--apple-blue);
            color: white;
            font-weight: var(--font-weight-semibold);
        }
        
        .dataTables_paginate .paginate_button.disabled {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-5);
            color: var(--text-tertiary);
            cursor: not-allowed;
        }
        
        .dataTables_paginate .paginate_button.disabled:hover {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-5);
            color: var(--text-tertiary);
            transform: none;
        }
        
        /* DataTables 底部布局优化 */
        .dataTables_wrapper .row {
            margin: 0;
            padding: var(--spacing-sm) var(--spacing-md);
            background: var(--bg-secondary);
            border-top: 1px solid var(--apple-gray-5);
        }
        
        .dataTables_wrapper .row:last-child {
            border-bottom-left-radius: var(--radius-xl);
            border-bottom-right-radius: var(--radius-xl);
        }
        
        .dataTables_wrapper .col-sm-12,
        .dataTables_wrapper .col-md-5,
        .dataTables_wrapper .col-md-7 {
            padding: 0;
        }
        
        .summary-toggle {
            cursor: pointer;
            color: var(--error-color);
            font-weight: bold;
            text-decoration: none;
        }
        
        .summary-toggle:hover {
            text-decoration: underline;
        }
        
        /* Apple Button System */
        .btn {
            border-radius: var(--radius-sm);
            font-weight: var(--font-weight-medium);
            font-size: var(--font-size-base);
            padding: var(--spacing-sm) var(--spacing-lg);
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border: none;
            letter-spacing: -0.01em;
            position: relative;
            overflow: hidden;
        }
        
        .btn-sm {
            font-size: var(--font-size-sm);
            padding: var(--spacing-xs) var(--spacing-md);
        }
        
        .btn-primary {
            background: var(--apple-blue);
            color: white;
            box-shadow: var(--shadow-sm);
        }
        
        .btn-primary:hover {
            background: var(--apple-blue-dark);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            color: white;
        }
        
        .btn-primary:active {
            transform: translateY(0);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-outline-primary {
            border: 1px solid var(--apple-blue);
            color: var(--apple-blue);
            background: transparent;
        }
        
        .btn-outline-primary:hover {
            background: var(--apple-blue);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }
        
        .btn-outline-secondary {
            border: 1px solid var(--apple-gray-4);
            color: var(--text-secondary);
            background: var(--bg-primary);
        }
        
        .btn-outline-secondary:hover {
            background: var(--bg-secondary);
            border-color: var(--apple-gray-3);
            color: var(--text-primary);
            transform: translateY(-1px);
        }
        
        /* Apple Table System */
        .table {
            border-collapse: separate;
            border-spacing: 0;
            font-size: var(--font-size-base);
        }
        
        .table thead th {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: none;
            border-bottom: 1px solid var(--apple-gray-4);
            font-weight: var(--font-weight-semibold);
            font-size: var(--font-size-sm);
            padding: var(--spacing-md) var(--spacing-sm);
            letter-spacing: -0.01em;
            text-align: left;
        }
        
        .table tbody tr {
            transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            border-bottom: 1px solid var(--apple-gray-5);
        }
        
        .table tbody tr:hover {
            background: rgba(0, 122, 255, 0.04);
            transform: translateY(-1px);
        }
        
        .table tbody tr:last-child {
            border-bottom: none;
        }
        
        .table tbody td {
            padding: var(--spacing-md) var(--spacing-sm);
            border-top: none;
            vertical-align: middle;
            font-size: var(--font-size-base);
            color: var(--text-primary);
        }
        
        .enhanced-datatable {
            background: var(--bg-elevated);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            border: 1px solid var(--apple-gray-5);
        }
        
        .table-controls {
            background: var(--bg-secondary);
            padding: var(--spacing-lg) var(--spacing-xl);
            border-bottom: 1px solid var(--apple-gray-5);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }
        
        /* Apple Responsive Design */
        @media (max-width: 768px) {
            .page-header {
                padding: var(--spacing-2xl) 0 var(--spacing-xl) 0;
            }
            
            .page-title {
                font-size: var(--font-size-3xl);
            }
            
            .page-subtitle {
                font-size: var(--font-size-base);
            }
            
            .table-controls {
                flex-direction: column;
                align-items: stretch;
                padding: var(--spacing-lg);
            }
            
            .metric-card {
                height: 110px;
            }
            
            .metric-card-content {
                padding: var(--spacing-lg) var(--spacing-md);
            }
            
            .metric-value {
                font-size: var(--font-size-3xl);
            }
            
            .time-filter-compact .d-flex {
                flex-direction: column;
                gap: var(--spacing-sm);
            }
            
            .custom-time-range .d-flex {
                flex-direction: column;
                gap: var(--spacing-xs);
            }
            
            .time-filter-compact .form-select,
            .time-filter-compact .form-control {
                width: 100% !important;
            }
            
            /* 移动端表格内边距调整 */
            .table thead th,
            .table tbody td {
                padding: var(--spacing-lg) var(--spacing-md);
            }
            
            /* 移动端DataTables样式调整 */
            .dataTables_length label {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-xs);
            }
            
            .dataTables_length select {
                margin: 0;
                width: 100px;
            }
            
            .dataTables_paginate .paginate_button {
                padding: var(--spacing-xs) var(--spacing-sm);
                margin: 0 1px;
                font-size: var(--font-size-xs);
            }
        }
        
        @media (max-width: 480px) {
            .page-header .d-flex {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-lg);
            }
            
            .time-filter-compact {
                width: 100%;
            }
        }

        /* 排序功能样式 */
        .sortable {
            cursor: pointer;
            user-select: none;
            transition: background-color 0.2s ease;
        }

        .sortable:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }

        .sort-icon {
            opacity: 0.5;
            transition: all 0.2s ease;
        }

        .sortable:hover .sort-icon {
            opacity: 1;
        }

        .sortable.sort-asc .sort-icon:before {
            content: "\f0de"; /* fa-sort-up */
            opacity: 1;
        }

        .sortable.sort-desc .sort-icon:before {
            content: "\f0dd"; /* fa-sort-down */
            opacity: 1;
        }
    </style>
</head>
<body>
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container-fluid">
                        <div class="d-flex align-items-center justify-content-between">
                <div>
                    <h1 class="page-title mb-0">
                        接口回放Diff详情分析
                    </h1>
                </div>
                <div class="text-end">
                    <div class="d-flex align-items-center gap-3">
                        <!-- 时间范围筛选器 -->
                        <div class="time-filter-compact">
                            <div class="d-flex align-items-center gap-2">
                                <select class="form-select form-select-sm" id="timePreset" style="width: 120px;">
                                    <option value="24h">最近24小时</option>
                                    <option value="7d">最近7天</option>
                                    <option value="30d">最近30天</option>
                                    <option value="custom">自定义</option>
                                </select>
                                <div class="custom-time-range" id="customTimeRange" style="display: none;">
                                    <div class="d-flex align-items-center gap-1">
                                        <input type="datetime-local" class="form-control form-control-sm" id="startTime" style="width: 140px;">
                                        <span class="text-muted small">至</span>
                                        <input type="datetime-local" class="form-control form-control-sm" id="endTime" style="width: 140px;">
                                    </div>
                                </div>
                                <button type="button" class="btn btn-primary btn-sm" id="applyTimeFilter">
                                    <i class="fas fa-filter"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="container-fluid">
        <!-- 数据概览 -->
        <div class="row mb-4 g-3">
            <div class="col-lg-3 col-md-6">
                <div class="metric-card card-info">
                    <div class="metric-card-content">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-layer-group"></i>
                            </div>
                            <div class="metric-label">总任务数</div>
                        </div>
                        <div class="metric-body">
                            <div class="metric-value" id="totalCount">
                                <div class="spinner-border spinner-border-sm text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="metric-card card-danger">
                    <div class="metric-card-content">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="metric-label">有差异任务</div>
                        </div>
                        <div class="metric-body">
                            <div class="metric-value" id="hasDiffCount">
                                <div class="spinner-border spinner-border-sm text-danger" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="metric-card card-success">
                    <div class="metric-card-content">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="metric-label">无差异任务</div>
                        </div>
                        <div class="metric-body">
                            <div class="metric-value" id="noDiffCount">
                                <div class="spinner-border spinner-border-sm text-success" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="metric-card card-warning">
                    <div class="metric-card-content">
                        <div class="metric-header">
                            <div class="metric-icon">
                                <i class="fas fa-hourglass-half"></i>
                            </div>
                            <div class="metric-label">未完成任务</div>
                        </div>
                        <div class="metric-body">
                            <div class="metric-value" id="unfinishedCount">
                                <div class="spinner-border spinner-border-sm text-warning" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="stats-summary mb-3">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h6 class="mb-0 fw-bold">
                    <i class="fas fa-chart-bar me-2 text-primary"></i>
                    统计概览
                    <small class="text-muted ms-2" id="statsTimeRange">（最近24小时）</small>
                </h6>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="toggleStatsOverview()">
                    <i class="fas fa-chevron-down me-1" id="statsToggleIcon"></i>
                    <span id="statsToggleText">展开</span>
                </button>
            </div>
            <div id="statsOverviewData" class="collapse">
                <div class="text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 增强数据表格 -->
        <div class="enhanced-datatable">
            <div class="table-controls">
                <div class="d-flex align-items-center justify-content-between w-100">
                <div class="d-flex align-items-center">
                    <h5 class="mb-0 me-3" style="font-weight: var(--font-weight-semibold); color: var(--text-primary);">
                        Diff结果列表
                    </h5>
                    <span id="searchStatus" class="badge ms-2" style="display: none; background: rgba(255, 149, 0, 0.1); color: var(--apple-orange); border: 1px solid rgba(255, 149, 0, 0.2); font-size: var(--font-size-xs); padding: var(--spacing-xs) var(--spacing-sm); border-radius: var(--radius-sm);">
                        <i class="fas fa-filter me-1"></i>已应用搜索条件
                    </span>
                </div>
                    <div class="d-flex align-items-center gap-3">
                        <!-- 接口筛选 -->
                        <div class="interface-filter">
                            <select class="form-select form-select-sm" id="interfaceFilter" style="width: 220px; max-width: 220px;">
                                <option value="">全部接口</option>
                                <!-- 选项通过loadInterfaceList()动态加载 -->
                            </select>
                        </div>
                        <!-- 状态筛选 -->
                        <div class="status-filter">
                            <select class="form-select form-select-sm" id="statusFilter" style="width: 120px;">
                                <option value="">全部状态</option>
                                <option value="0">未完成</option>
                                <option value="1">已完成</option>
                                <option value="2">失败</option>
                            </select>
                        </div>
                        <!-- 搜索框 -->
                <div class="table-search">
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control form-control-sm" id="tableSearch" placeholder="搜索接口名、指纹..." style="width: 200px;">
                                <button class="btn btn-outline-secondary btn-sm" type="button" id="clearSearch" title="清除搜索">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-responsive">
                <table id="diffDataTable" class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th width="200">接口信息</th>
                            <th width="180">请求参数</th>
                            <th width="80">差异数</th>
                            <th width="150">旧数据</th>
                            <th width="150">新数据</th>
                            <th width="120">状态</th>
                            <th width="120" class="sortable" data-sort="createTime">
                                创建时间
                                <i class="fas fa-sort ms-1 sort-icon"></i>
                            </th>
                            <th width="120" class="sortable" data-sort="updateTime">
                                更新时间
                                <i class="fas fa-sort ms-1 sort-icon"></i>
                            </th>
                            <th width="120">操作</th>
                        </tr>
                    </thead>
                    <tbody id="diffTableBody">
                        <tr>
                            <td colspan="9" class="text-center py-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                                <div class="mt-2 text-muted">正在加载数据...</div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <!-- 数据查看模态框 -->
    <div class="modal fade" id="dataModal" tabindex="-1" aria-labelledby="dataModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="dataModalLabel">
                        <i class="fas fa-code me-2"></i>
                        数据详情
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="json-viewer">
                        <pre id="dataModalContent"></pre>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" onclick="copyToClipboard()">
                        <i class="fas fa-copy me-1"></i>复制
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Diff结果模态框 -->
    <div class="modal fade" id="diffDetailModal" tabindex="-1" aria-labelledby="diffDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="diffDetailModalLabel">
                        <i class="fas fa-code-branch me-2"></i>
                        Diff结果对比
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <ul class="nav nav-tabs" id="diffTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="params-tab" data-bs-toggle="tab" data-bs-target="#params" type="button" role="tab">
                                <i class="fas fa-cog me-1"></i>请求参数
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="old-data-tab" data-bs-toggle="tab" data-bs-target="#old-data" type="button" role="tab">
                                <i class="fas fa-history me-1"></i>旧数据
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="new-data-tab" data-bs-toggle="tab" data-bs-target="#new-data" type="button" role="tab">
                                <i class="fas fa-plus me-1"></i>新数据
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="diffTabContent">
                        <div class="tab-pane fade show active" id="params" role="tabpanel">
                            <div class="json-viewer mt-3">
                                <pre id="diffParams"></pre>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="old-data" role="tabpanel">
                            <div class="json-viewer mt-3">
                                <pre id="diffOldData"></pre>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="new-data" role="tabpanel">
                            <div class="json-viewer mt-3">
                                <pre id="diffNewData"></pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <a id="diffResultLink" href="#" class="btn btn-primary" target="_blank">
                        <i class="fas fa-external-link-alt me-1"></i>查看Diff结果
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS 和依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        let statsOverviewLoaded = false;
        let currentModalContent = '';
        let currentTimeRange = { preset: '24h', startTime: null, endTime: null };
        let currentSort = { field: 'updateTime', order: 'desc' };
        let pageDataLoaded = false; // 防止重复加载
        let dataTablesInitialized = false; // 防止重复初始化DataTables

        // 初始化DataTables
        function initializeDataTables() {
            if (dataTablesInitialized) {
                console.log('DataTables已初始化，跳过');
                return;
            }

            try {
                // 检查表格是否存在且有正确的结构
                const table = $('#diffDataTable');
                if (table.length && table.find('thead').length && table.find('tbody').length) {
                    // 如果已经初始化过，先销毁
                    if ($.fn.DataTable.isDataTable('#diffDataTable')) {
                        $('#diffDataTable').DataTable().destroy();
                    }

                    $('#diffDataTable').DataTable({
                        responsive: true,
                        language: {
                            url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/zh.json'
                        },
                        order: [[6, 'desc']], // 按更新时间降序
                        pageLength: 25,
                        searching: false, // 禁用内置搜索，使用自定义搜索
                        destroy: true // 允许重新初始化
                    });

                    dataTablesInitialized = true;
                    console.log('DataTables 初始化成功');
                } else {
                    console.error('表格结构不完整，无法初始化DataTables');
                }
            } catch (error) {
                console.error('DataTables 初始化失败:', error);
            }
        }

        // 设置默认时间范围（最近24小时）
        function setDefaultTimeRange() {
            const endTime = moment();
            const startTime = moment().subtract(24, 'hours');

            currentTimeRange = {
                preset: '24h',
                startTime: startTime.unix(),
                endTime: endTime.unix()
            };

            console.log('设置默认时间范围:', currentTimeRange);

            // 设置UI显示
            $('#timePreset').val('24h');
            updateStatsTimeRangeDisplay('24h', startTime, endTime);
        }
        
        // 初始化页面
        $(document).ready(function() {
            // 初始化时间显示
            moment.locale('zh-cn');

            // 初始化全局时间筛选器
            initTimeFilter();

            // 从URL参数恢复时间范围
            initTimeRangeFromURL();

            // 不在这里初始化DataTables，等数据加载完成后再初始化
            console.log('跳过DataTables初始化，等待数据加载完成');

            // 绑定筛选事件
            bindFilterEvents();

            // 绑定排序事件
            bindSortEvents();

            // 初始化排序图标
            updateSortIcons();

            // 确保时间范围设置完成后再加载数据
            setTimeout(() => {
                console.log('=== 页面初始化开始 ===');
                console.log('当前时间范围:', currentTimeRange);

                // 确保时间范围已初始化
                if (!currentTimeRange.startTime || !currentTimeRange.endTime) {
                    console.log('时间范围未初始化，使用默认时间范围');
                    setDefaultTimeRange();
                }

                console.log('最终时间范围:', currentTimeRange);
                console.log('开始加载页面数据...');

                // 强制加载数据，即使有其他问题
                try {
                    loadPageData();
                } catch (error) {
                    console.error('loadPageData 执行失败:', error);
                    // 如果loadPageData失败，直接调用各个API
                    console.log('尝试直接调用各个API...');
                    loadOverviewData().catch(e => console.error('概览数据加载失败:', e));
                    loadDiffData().catch(e => console.error('差异数据加载失败:', e));
                    loadInterfaceList().catch(e => console.error('接口列表加载失败:', e));
                }
            }, 300);
        });

        // 从URL参数初始化时间范围
        function initTimeRangeFromURL() {
            console.log('初始化时间范围从URL参数...');
            const urlParams = new URLSearchParams(window.location.search);
            const startTime = urlParams.get('startTime');
            const endTime = urlParams.get('endTime');

            console.log('URL参数 - startTime:', startTime, 'endTime:', endTime);

            if (startTime && endTime) {
                // 从URL参数恢复时间范围
                const startMoment = moment.unix(startTime);
                const endMoment = moment.unix(endTime);

                currentTimeRange = {
                    preset: 'custom', // 先设为custom，后面会自动检测
                    startTime: parseInt(startTime),
                    endTime: parseInt(endTime)
                };

                // 自动检测时间范围类型并设置UI
                detectTimeRangeType();

                // 如果是自定义时间范围，设置自定义时间输入框的值
                if (currentTimeRange.preset === 'custom') {
                    $('#startTime').val(startMoment.format('YYYY-MM-DDTHH:mm'));
                    $('#endTime').val(endMoment.format('YYYY-MM-DDTHH:mm'));
                }

                // 更新显示
                const preset = currentTimeRange.preset;
                if (preset === 'custom') {
                    updateStatsTimeRangeDisplay('custom', startMoment, endMoment);
                } else {
                    updateStatsTimeRangeDisplay(preset);
                }

                console.log('从URL恢复时间范围:', startMoment.format('YYYY-MM-DD HH:mm'), '至', endMoment.format('YYYY-MM-DD HH:mm'), '类型:', preset);
            } else {
                // 没有URL参数时使用默认时间范围
                console.log('没有URL参数，使用默认时间范围');
                setDefaultTimeRange();
            }
        }

        // 检测并设置时间范围类型
        function detectTimeRangeType() {
            if (!currentTimeRange.startTime || !currentTimeRange.endTime) {
                return;
            }

            const now = moment();
            const startMoment = moment.unix(currentTimeRange.startTime);
            const endMoment = moment.unix(currentTimeRange.endTime);

            // 检查是否匹配预设时间范围（允许5分钟误差）
            const tolerance = 5 * 60; // 5分钟

            // 检查24小时
            const start24h = now.clone().subtract(24, 'hours');
            if (Math.abs(startMoment.unix() - start24h.unix()) < tolerance &&
                Math.abs(endMoment.unix() - now.unix()) < tolerance) {
                $('#timePreset').val('24h');
                $('#customTimeRange').hide();
                currentTimeRange.preset = '24h';
                return;
            }

            // 检查7天
            const start7d = now.clone().subtract(7, 'days');
            if (Math.abs(startMoment.unix() - start7d.unix()) < tolerance &&
                Math.abs(endMoment.unix() - now.unix()) < tolerance) {
                $('#timePreset').val('7d');
                $('#customTimeRange').hide();
                currentTimeRange.preset = '7d';
                return;
            }

            // 检查30天
            const start30d = now.clone().subtract(30, 'days');
            if (Math.abs(startMoment.unix() - start30d.unix()) < tolerance &&
                Math.abs(endMoment.unix() - now.unix()) < tolerance) {
                $('#timePreset').val('30d');
                $('#customTimeRange').hide();
                currentTimeRange.preset = '30d';
                return;
            }

            // 如果都不匹配，则为自定义时间范围
            $('#timePreset').val('custom');
            $('#customTimeRange').show();
            currentTimeRange.preset = 'custom';

            console.log('检测为自定义时间范围，显示自定义时间输入框');
        }

        // 初始化时间筛选器
        function initTimeFilter() {
            $('#timePreset').on('change', function() {
                const preset = $(this).val();
                if (preset === 'custom') {
                    $('#customTimeRange').show();
                    setCustomTimeInputs();
                } else {
                    $('#customTimeRange').hide();
                    updateTimeRangeByPreset(preset);
                    // 预设时间范围变化时立即更新URL参数
                    updateURLParams();
                }
            });

            $('#applyTimeFilter').on('click', function() {
                applyTimeFilter();
            });
        }

        // 更新URL参数（不刷新页面）
        function updateURLParams() {
            if (currentTimeRange.startTime && currentTimeRange.endTime) {
                const params = new URLSearchParams();
                params.append('startTime', currentTimeRange.startTime);
                params.append('endTime', currentTimeRange.endTime);

                const newURL = window.location.pathname + '?' + params.toString();
                window.history.replaceState({}, '', newURL);

                console.log('更新URL参数:', newURL);
            }
        }

        // 设置默认时间范围
        function setDefaultTimeRange() {
            updateTimeRangeByPreset('24h');
            $('#timePreset').val('24h');
            updateURLParams();
        }
        
        // 根据预设更新时间范围
        function updateTimeRangeByPreset(preset) {
            const now = moment();
            let startTime, endTime;
            
            switch(preset) {
                case '24h':
                    startTime = now.clone().subtract(24, 'hours');
                    endTime = now;
                    break;
                case '7d':
                    startTime = now.clone().subtract(7, 'days');
                    endTime = now;
                    break;
                case '30d':
                    startTime = now.clone().subtract(30, 'days');
                    endTime = now;
                    break;
            }
            
            currentTimeRange = {
                preset: preset,
                startTime: startTime.unix(),
                endTime: endTime.unix()
            };
            
            updateStatsTimeRangeDisplay(preset);
        }
        
        // 设置自定义时间输入框
        function setCustomTimeInputs() {
            const now = moment();
            const yesterday = now.clone().subtract(1, 'day');
            
            $('#startTime').val(yesterday.format('YYYY-MM-DDTHH:mm'));
            $('#endTime').val(now.format('YYYY-MM-DDTHH:mm'));
        }
        
        // 应用时间筛选
        function applyTimeFilter() {
            const preset = $('#timePreset').val();

            if (preset === 'custom') {
                const startTimeStr = $('#startTime').val();
                const endTimeStr = $('#endTime').val();

                if (!startTimeStr || !endTimeStr) {
                    alert('请选择开始时间和结束时间');
                    return;
                }

                const startTime = moment(startTimeStr);
                const endTime = moment(endTimeStr);

                if (startTime.isAfter(endTime)) {
                    alert('开始时间不能晚于结束时间');
                    return;
                }

                currentTimeRange = {
                    preset: 'custom',
                    startTime: startTime.unix(),
                    endTime: endTime.unix()
                };

                updateStatsTimeRangeDisplay('custom', startTime, endTime);
            } else {
                updateTimeRangeByPreset(preset);
            }

            console.log('应用时间筛选，新的时间范围:', currentTimeRange);

            // 更新URL参数
            updateURLParams();

            // 重新加载所有数据
            reloadAllData();
        }
        
        // 更新统计时间范围显示
        function updateStatsTimeRangeDisplay(preset, startTime = null, endTime = null) {
            let displayText = '';
            switch(preset) {
                case '24h':
                    displayText = '（最近24小时）';
                    break;
                case '7d':
                    displayText = '（最近7天）';
                    break;
                case '30d':
                    displayText = '（最近30天）';
                    break;
                case 'custom':
                    if (startTime && endTime) {
                        displayText = `（${startTime.format('MM-DD HH:mm')} 至 ${endTime.format('MM-DD HH:mm')}）`;
                    }
                    break;
            }
            $('#statsTimeRange').text(displayText);
        }
        
        // 加载页面数据（分别调用三个接口）
        function loadPageData() {
            if (pageDataLoaded) {
                console.log('页面数据已加载，跳过重复加载');
                return;
            }

            console.log('开始加载页面数据...', currentTimeRange);

            // 确保时间范围已设置
            if (!currentTimeRange.startTime || !currentTimeRange.endTime) {
                console.error('时间范围未设置，无法加载数据');
                return;
            }

            pageDataLoaded = true;

            // 并发加载三个接口的数据
            Promise.all([
                loadOverviewData(),
                loadDiffData(),
                loadInterfaceList()
            ]).then(() => {
                console.log('所有页面数据加载完成');
            }).catch(error => {
                console.error('加载页面数据失败:', error);
                pageDataLoaded = false; // 重置标志，允许重试
            });
        }

        // 加载概览数据
        function loadOverviewData() {
            return new Promise((resolve, reject) => {
                // 构建查询参数
                const params = new URLSearchParams();
                if (currentTimeRange.startTime) {
                    params.append('startTime', currentTimeRange.startTime);
                }
                if (currentTimeRange.endTime) {
                    params.append('endTime', currentTimeRange.endTime);
                }

                const url = '/fwyytool/tools/diff/overview' + (params.toString() ? '?' + params.toString() : '');
                console.log('请求概览数据:', url);

                fetch(url)
                    .then(response => {
                        console.log('概览数据响应状态:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('概览数据响应:', data);
                        if (data.code === 0 && data.data) {
                            updateOverviewMetrics(data.data);
                            resolve(data.data);
                        } else {
                            console.error('概览数据API响应错误:', data.message || '未知错误');
                            showOverviewError();
                            reject(new Error(data.message || '加载概览数据失败'));
                        }
                    })
                    .catch(error => {
                        console.error('请求概览数据失败:', error);
                        showOverviewError();
                        reject(error);
                    });
            });
        }

        // 更新概览指标
        function updateOverviewMetrics(data) {
            // 更新四个指标卡片
            document.getElementById('totalCount').textContent = data.total || 0;
            document.getElementById('hasDiffCount').textContent = data.hasDiffNum || 0;
            document.getElementById('noDiffCount').textContent = data.noDiffNum || 0;
            document.getElementById('unfinishedCount').textContent = data.unFinishedTask || 0;
            
            console.log('概览数据更新完成:', data);
        }

        // 显示概览数据加载错误
        function showOverviewError() {
            document.getElementById('totalCount').innerHTML = '<span class="text-danger">-</span>';
            document.getElementById('hasDiffCount').innerHTML = '<span class="text-danger">-</span>';
            document.getElementById('noDiffCount').innerHTML = '<span class="text-danger">-</span>';
            document.getElementById('unfinishedCount').innerHTML = '<span class="text-danger">-</span>';
        }

        // 加载差异数据列表
        function loadDiffData() {
            return new Promise((resolve, reject) => {
                // 构建查询参数
                const params = new URLSearchParams();
                if (currentTimeRange.startTime) {
                    params.append('startTime', currentTimeRange.startTime);
                }
                if (currentTimeRange.endTime) {
                    params.append('endTime', currentTimeRange.endTime);
                }

                // 添加默认分页参数
                params.append('page', '1');
                params.append('pageSize', '100');

                // 添加排序参数
                params.append('sortBy', currentSort.field);
                params.append('sortOrder', currentSort.order);

                const url = '/fwyytool/tools/diff/res' + (params.toString() ? '?' + params.toString() : '');
                console.log('请求差异数据:', url);

                fetch(url)
                    .then(response => {
                        console.log('差异数据响应状态:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('差异数据响应:', data);
                        if (data.code === 0 && data.data) {
                            updateDiffTable(data.data);
                            resolve(data.data);
                        } else {
                            console.error('差异数据API响应错误:', data.message || '未知错误');
                            showDiffTableError();
                            reject(new Error(data.message || '加载差异数据失败'));
                        }
                    })
                    .catch(error => {
                        console.error('请求差异数据失败:', error);
                        showDiffTableError();
                        reject(error);
                    });
            });
        }

        // 更新差异数据表格
        function updateDiffTable(data) {
            const tbody = document.getElementById('diffTableBody');
            
            if (!data.dataDiffList || data.dataDiffList.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="9" class="text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-info-circle me-2"></i>
                                暂无数据
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            let rows = '';
            data.dataDiffList.forEach(diffInfo => {
                const statusText = diffInfo.status === 0 ? '未完成' : 
                                 diffInfo.status === 1 ? '已完成' : 
                                 diffInfo.status === 2 ? '失败' : '未知';
                
                const diffTypeText = diffInfo.diffType === 0 ? '新老对比' : '环比对比';
                
                const paramsPreview = diffInfo.params ? 
                    (diffInfo.params.length > 100 ? diffInfo.params.substring(0, 100) + '...' : diffInfo.params) : 
                    '';

                rows += `
                    <tr data-id="${diffInfo.id || diffInfo.ID}" data-status="${diffInfo.status}" data-diff-type="${diffInfo.diffType}">
                        <td>
                            <div class="fw-bold text-primary">${diffInfo.handlerName || diffInfo.handler_name || ''}</div>
                            <small class="text-muted font-monospace">${diffInfo.fingerprint || ''}</small>
                            <div class="mt-1">
                                <span class="badge bg-secondary">${diffTypeText}</span>
                            </div>
                        </td>
                        <td>
                            <div class="params-preview" onclick="showParamsModal('${diffInfo.id || diffInfo.ID}', '${escapeHtml(diffInfo.params || '')}')" title="点击查看完整参数">
                                ${paramsPreview ? paramsPreview : '<span class="text-muted">无参数</span>'}
                            </div>
                        </td>
                        <td>
                            <span class="badge ${diffInfo.diffNum > 0 ? 'bg-danger' : 'bg-success'} fs-6">
                                ${diffInfo.diffNum || 0}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-outline-secondary btn-sm" onclick="showDataModal('${diffInfo.id || diffInfo.ID}', 'old', '${escapeHtml(diffInfo.oldData || '')}')">
                                <i class="fas fa-eye me-1"></i>查看
                            </button>
                        </td>
                        <td>
                            <button class="btn btn-outline-secondary btn-sm" onclick="showDataModal('${diffInfo.id || diffInfo.ID}', 'new', '${escapeHtml(diffInfo.newData || '')}')">
                                <i class="fas fa-eye me-1"></i>查看
                            </button>
                        </td>
                        <td>
                            <span class="status-badge status-${diffInfo.status}">
                                ${statusText}
                            </span>
                        </td>
                        <td>
                            <div class="text-nowrap">
                                ${formatTime(diffInfo.createTime)}
                            </div>
                        </td>
                        <td>
                            <div class="text-nowrap">
                                ${formatTime(diffInfo.updateTime)}
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewDiffDetail('${diffInfo.id || diffInfo.ID}', '${escapeHtml(diffInfo.params || '')}', '${escapeHtml(diffInfo.oldData || '')}', '${escapeHtml(diffInfo.newData || '')}', '${diffInfo.diffResult || ''}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                ${diffInfo.diffResult ? `
                                <a href="${diffInfo.diffResult}" class="btn btn-outline-success btn-sm" target="_blank" title="查看Diff结果">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            });

            tbody.innerHTML = rows;
            console.log('差异数据表格更新完成，共', data.dataDiffList.length, '条记录');

            // 数据加载完成后初始化DataTables
            initializeDataTables();
        }

        // 显示差异表格加载错误
        function showDiffTableError() {
            const tbody = document.getElementById('diffTableBody');
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center py-5">
                        <div class="text-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            数据加载失败，请刷新页面重试
                        </div>
                    </td>
                </tr>
            `;
        }

        // HTML转义函数
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML.replace(/'/g, '&#39;').replace(/"/g, '&quot;');
        }

        // 时间格式化函数
        function formatTime(timestamp) {
            if (!timestamp) return '-';
            return moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss');
        }

        // 重新加载所有数据
        function reloadAllData() {
            console.log('重新加载所有数据...');

            // 重置加载状态
            pageDataLoaded = false;
            statsOverviewLoaded = false;

            const statsContainer = document.getElementById('statsOverviewData');
            if (statsContainer.classList.contains('show')) {
                statsContainer.classList.remove('show');
                document.getElementById('statsToggleText').textContent = '展开';
                document.getElementById('statsToggleIcon').className = 'fas fa-chevron-down me-1';
            }

            // 重新加载页面数据
            loadPageData();
        }
        
        // 绑定筛选事件
        function bindFilterEvents() {
            // 接口筛选
            $('#interfaceFilter').on('change', function() {
                applyAdvancedSearch();
            });

            // 状态筛选
            $('#statusFilter').on('change', function() {
                applyAdvancedSearch();
            });

            // 搜索框 - 添加防抖处理
            let searchTimeout;
            $('#tableSearch').on('keyup', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    applyAdvancedSearch();
                }, 300); // 300ms 防抖
            });

            // 搜索框回车事件
            $('#tableSearch').on('keypress', function(e) {
                if (e.which === 13) { // Enter键
                    clearTimeout(searchTimeout);
                    applyAdvancedSearch();
                }
            });

            // 清除搜索按钮
            $('#clearSearch').on('click', function() {
                $('#tableSearch').val('');
                $('#statusFilter').val('');
                $('#interfaceFilter').val('');

                // 重置排序为默认状态
                currentSort = { field: 'updateTime', order: 'desc' };
                updateSortIcons();

                applyAdvancedSearch();
            });
        }

        // 绑定排序事件
        function bindSortEvents() {
            $('.sortable').on('click', function() {
                const sortField = $(this).data('sort');

                // 切换排序方向
                if (currentSort.field === sortField) {
                    currentSort.order = currentSort.order === 'asc' ? 'desc' : 'asc';
                } else {
                    currentSort.field = sortField;
                    currentSort.order = 'desc'; // 默认降序
                }

                // 更新排序图标
                updateSortIcons();

                // 应用排序
                applyAdvancedSearch();
            });
        }

        // 更新排序图标
        function updateSortIcons() {
            // 清除所有排序状态
            $('.sortable').removeClass('sort-asc sort-desc');

            // 设置当前排序状态
            const currentHeader = $(`.sortable[data-sort="${currentSort.field}"]`);
            currentHeader.addClass(currentSort.order === 'asc' ? 'sort-asc' : 'sort-desc');
        }

        // 应用表格筛选
        function applyTableFilters() {
            const interfaceFilter = $('#interfaceFilter').val().toLowerCase();
            const statusFilter = $('#statusFilter').val();
            const searchTerm = $('#tableSearch').val().toLowerCase();
            
                $('#diffDataTable tbody tr').each(function() {
                    const row = $(this);
                const interfaceName = row.find('td:first .fw-bold').text().toLowerCase();
                const status = row.data('status').toString();
                const rowText = row.text().toLowerCase();
                
                let showRow = true;
                
                // 接口筛选
                if (interfaceFilter && interfaceName.indexOf(interfaceFilter) === -1) {
                    showRow = false;
                }
                
                // 状态筛选
                if (statusFilter && status !== statusFilter) {
                    showRow = false;
                }
                
                // 文本搜索
                if (searchTerm && rowText.indexOf(searchTerm) === -1) {
                    showRow = false;
                }
                
                if (showRow) {
                        row.show();
                    } else {
                        row.hide();
                    }
                });
        }
        
        // 加载接口列表，支持搜索过滤
        function loadInterfaceList(searchParams = {}) {
            return new Promise((resolve, reject) => {
                // 构建查询参数
                const params = new URLSearchParams();

                // 添加时间范围参数
                if (currentTimeRange.startTime) {
                    params.append('startTime', currentTimeRange.startTime);
                }
                if (currentTimeRange.endTime) {
                    params.append('endTime', currentTimeRange.endTime);
                }

                // 添加搜索参数
                Object.keys(searchParams).forEach(key => {
                    if (searchParams[key] !== undefined && searchParams[key] !== null && searchParams[key] !== '') {
                        if (Array.isArray(searchParams[key])) {
                            searchParams[key].forEach(value => params.append(key, value));
                        } else {
                            params.append(key, searchParams[key]);
                        }
                    }
                });

                // 添加分页参数用于获取接口列表，使用较大的pageSize获取更多数据
                params.append('page', '1');
                params.append('pageSize', '500'); // 减少pageSize，避免请求过大

                const url = '/fwyytool/tools/diff/res' + (params.toString() ? '?' + params.toString() : '');
                console.log('请求接口列表:', url);

                fetch(url)
                    .then(response => {
                        console.log('接口列表响应状态:', response.status);
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('接口列表响应:', data);
                        if (data.code === 0 && data.data && data.data.dataDiffList) {
                            const select = document.getElementById('interfaceFilter');

                            // 清空现有选项（保留第一个空选项）
                            while (select.children.length > 1) {
                                select.removeChild(select.lastChild);
                            }

                            // 从diff结果中提取唯一的接口名列表
                            const handlerNames = [...new Set(data.data.dataDiffList.map(item => item.handlerName || item.handler_name))];
                            
                            // 添加从API获取的选项
                            handlerNames.forEach(handler => {
                                const option = document.createElement('option');
                                option.value = handler;
                                option.textContent = handler;
                                select.appendChild(option);
                            });

                            console.log('接口列表加载成功，共', handlerNames.length, '个接口');
                            resolve(handlerNames);
                        } else {
                            console.error('加载接口列表失败:', data.message || '未知错误');
                            reject(new Error(data.message || '加载接口列表失败'));
                        }
                    })
                    .catch(error => {
                        console.error('请求接口列表失败:', error);
                        reject(error);
                    });
            });
        }

        // 使用增强的搜索接口进行数据搜索
        function searchDiffData(searchParams = {}) {
            // 构建查询参数
            const params = new URLSearchParams();

            // 添加时间范围参数
            if (currentTimeRange.startTime) {
                params.append('startTime', currentTimeRange.startTime);
            }
            if (currentTimeRange.endTime) {
                params.append('endTime', currentTimeRange.endTime);
            }

            // 添加搜索参数
            Object.keys(searchParams).forEach(key => {
                if (searchParams[key] !== undefined && searchParams[key] !== null && searchParams[key] !== '') {
                    if (Array.isArray(searchParams[key])) {
                        searchParams[key].forEach(value => params.append(key, value));
                    } else {
                        params.append(key, searchParams[key]);
                    }
                }
            });

            const url = '/fwyytool/tools/diff/res' + (params.toString() ? '?' + params.toString() : '');
            console.log('搜索数据请求:', url);

            return fetch(url)
                .then(response => {
                    console.log('搜索数据响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('搜索数据响应:', data);
                    if (data.code === 0) {
                        return data.data;
                    } else {
                        throw new Error(data.message || '搜索失败');
                    }
                })
                .catch(error => {
                    console.error('搜索数据失败:', error);
                    throw error;
                });
        }

        // 应用高级搜索和过滤
        function applyAdvancedSearch() {
            const searchTerm = $('#tableSearch').val().trim();
            const statusFilter = $('#statusFilter').val();
            const interfaceFilter = $('#interfaceFilter').val();

            // 构建搜索参数
            const searchParams = {};

            // 添加时间范围参数
            if (currentTimeRange.startTime) {
                searchParams.startTime = currentTimeRange.startTime;
            }
            if (currentTimeRange.endTime) {
                searchParams.endTime = currentTimeRange.endTime;
            }

            if (searchTerm) {
                searchParams.search = searchTerm;
            }

            if (statusFilter) {
                searchParams.status = [parseInt(statusFilter)];
            }

            if (interfaceFilter) {
                searchParams.handlerNames = [interfaceFilter];
            }

            // 添加排序参数
            searchParams.sortBy = currentSort.field;
            searchParams.sortOrder = currentSort.order;

            // 添加分页参数
            searchParams.page = 1;
            searchParams.pageSize = 100;

            // 更新搜索状态指示器 - 只有真正的搜索条件才显示状态
            const hasUserSearchConditions = searchTerm || statusFilter || interfaceFilter;
            const searchStatus = $('#searchStatus');
            if (hasUserSearchConditions) {
                searchStatus.show();
            } else {
                searchStatus.hide();
            }

            // 如果有搜索条件，使用服务端搜索
            if (hasUserSearchConditions) {
                console.log('执行服务端搜索，参数:', searchParams);
                searchDiffData(searchParams)
                    .then(result => {
                        updateTableWithSearchResults(result);
                    })
                    .catch(error => {
                        console.error('搜索失败:', error);
                        // 降级到客户端过滤
                        applyTableFilters();
                    });
            } else {
                // 无搜索条件时使用客户端过滤，避免重复调用API
                console.log('无搜索条件，使用客户端过滤');
                applyTableFilters();
            }
        }

        // 使用搜索结果更新表格
        function updateTableWithSearchResults(searchResult) {
            if (!searchResult || !searchResult.dataDiffList) {
                console.error('搜索结果格式错误');
                return;
            }

            const tbody = $('#diffDataTable tbody');
            const allRows = tbody.find('tr');

            // 隐藏所有行
            allRows.hide();

            // 显示匹配的行
            searchResult.dataDiffList.forEach(item => {
                // 适配新的ID字段名称
                const itemId = item.id || item.ID;
                const matchingRow = allRows.filter(`[data-id="${itemId}"]`);
                matchingRow.show();
            });

            console.log(`搜索完成，显示 ${searchResult.dataDiffList.length} 条记录，总计 ${searchResult.total} 条`);
        }

        // 切换统计概览
        function toggleStatsOverview() {
            const container = document.getElementById('statsOverviewData');
            const toggleText = document.getElementById('statsToggleText');
            const toggleIcon = document.getElementById('statsToggleIcon');
            
            if (container.classList.contains('show')) {
                container.classList.remove('show');
                toggleText.textContent = '展开';
                toggleIcon.className = 'fas fa-chevron-down me-1';
                return;
            }
            
            if (statsOverviewLoaded) {
                container.classList.add('show');
                toggleText.textContent = '收起';
                toggleIcon.className = 'fas fa-chevron-up me-1';
                return;
            }
            
            // 构建请求参数（支持时间范围）
            const params = new URLSearchParams();
            if (currentTimeRange.startTime && currentTimeRange.endTime) {
                params.append('startTime', currentTimeRange.startTime);
                params.append('endTime', currentTimeRange.endTime);
            }
            
            // 加载数据
            const url = '/fwyytool/tools/diff/count' + (params.toString() ? '?' + params.toString() : '');
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 0 && data.data) {
                        const statsData = data.data;
                        const sortableData = Object.entries(statsData).map(([key, value]) => ({
                            name: key,
                            ...value
                        })).sort((a, b) => b.hasDiffCnt - a.hasDiffCnt);
                        
                        let tableHtml = `
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-success">
                                        <tr>
                                            <th>接口名称</th>
                                            <th class="text-center">有差异</th>
                                            <th class="text-center">无差异</th>
                                            <th class="text-center">未完成</th>
                                            <th class="text-center">失败</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                        `;
                        
                        sortableData.forEach(item => {
                            const hasDiffClass = item.hasDiffCnt > 0 ? 'text-danger fw-bold' : '';
                            tableHtml += `
                                <tr>
                                    <td class="font-monospace">${item.name}</td>
                                    <td class="text-center ${hasDiffClass}">${item.hasDiffCnt}</td>
                                    <td class="text-center">${item.noDiffCnt}</td>
                                    <td class="text-center">${item.unFinishCnt}</td>
                                    <td class="text-center">${item.failedCnt || item.failedCun || 0}</td>
                                </tr>
                            `;
                        });
                        
                        tableHtml += '</tbody></table></div>';
                        container.innerHTML = tableHtml;
                    } else {
                        container.innerHTML = '<div class="alert alert-warning">数据加载失败</div>';
                    }
                    
                    container.classList.add('show');
                    toggleText.textContent = '收起';
                    toggleIcon.className = 'fas fa-chevron-up me-1';
                    statsOverviewLoaded = true;
                })
                .catch(error => {
                    container.innerHTML = `<div class="alert alert-danger">获取数据失败: ${error.message}</div>`;
                    container.classList.add('show');
                    toggleText.textContent = '收起';
                    toggleIcon.className = 'fas fa-chevron-up me-1';
                });
        }
        
        // 显示参数模态框
        function showParamsModal(id, params) {
            try {
                const formatted = JSON.stringify(JSON.parse(params), null, 2);
                document.getElementById('dataModalContent').textContent = formatted;
                document.getElementById('dataModalLabel').innerHTML = '<i class="fas fa-cog me-2"></i>请求参数 - ID: ' + id;
                currentModalContent = formatted;
                new bootstrap.Modal(document.getElementById('dataModal')).show();
            } catch (e) {
                document.getElementById('dataModalContent').textContent = params;
                document.getElementById('dataModalLabel').innerHTML = '<i class="fas fa-cog me-2"></i>请求参数 - ID: ' + id;
                currentModalContent = params;
                new bootstrap.Modal(document.getElementById('dataModal')).show();
            }
        }
        
        // 显示数据模态框
        function showDataModal(id, type, data) {
            try {
                const formatted = JSON.stringify(JSON.parse(data), null, 2);
                document.getElementById('dataModalContent').textContent = formatted;
                const title = type === 'old' ? '旧数据' : '新数据';
                document.getElementById('dataModalLabel').innerHTML = `<i class="fas fa-database me-2"></i>${title} - ID: ${id}`;
                currentModalContent = formatted;
                new bootstrap.Modal(document.getElementById('dataModal')).show();
            } catch (e) {
                document.getElementById('dataModalContent').textContent = data;
                const title = type === 'old' ? '旧数据' : '新数据';
                document.getElementById('dataModalLabel').innerHTML = `<i class="fas fa-database me-2"></i>${title} - ID: ${id}`;
                currentModalContent = data;
                new bootstrap.Modal(document.getElementById('dataModal')).show();
            }
        }
        
        // 显示Diff详情
        function viewDiffDetail(id, params, oldData, newData, diffResult) {
            try {
                // 格式化JSON
                const formatJSON = (str) => {
                    try {
                        return JSON.stringify(JSON.parse(str), null, 2);
                    } catch {
                        return str;
                    }
                };
                
                document.getElementById('diffParams').textContent = formatJSON(params);
                document.getElementById('diffOldData').textContent = formatJSON(oldData);
                document.getElementById('diffNewData').textContent = formatJSON(newData);
                
                const linkBtn = document.getElementById('diffResultLink');
                if (diffResult) {
                    linkBtn.href = diffResult;
                    linkBtn.style.display = 'inline-block';
                } else {
                    linkBtn.style.display = 'none';
                }
                
                new bootstrap.Modal(document.getElementById('diffDetailModal')).show();
            } catch (e) {
                console.error('显示Diff详情错误:', e);
            }
        }
        
        // 复制到剪贴板
        function copyToClipboard() {
            navigator.clipboard.writeText(currentModalContent).then(() => {
                // 显示成功提示
                const toast = document.createElement('div');
                toast.className = 'toast-container position-fixed top-0 end-0 p-3';
                toast.innerHTML = `
                    <div class="toast show" role="alert">
                        <div class="toast-header">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong class="me-auto">成功</strong>
                            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                        </div>
                        <div class="toast-body">内容已复制到剪贴板</div>
                    </div>
                `;
                document.body.appendChild(toast);
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 3000);
            }).catch(err => {
                console.error('复制失败:', err);
            });
        }

        // 备用初始化机制 - 确保页面一定会加载数据
        window.addEventListener('load', function() {
            console.log('=== Window Load 事件触发 ===');
            setTimeout(() => {
                if (!pageDataLoaded) {
                    console.log('检测到页面数据未加载，强制初始化...');

                    // 强制设置时间范围
                    if (!currentTimeRange.startTime || !currentTimeRange.endTime) {
                        const urlParams = new URLSearchParams(window.location.search);
                        const startTime = urlParams.get('startTime');
                        const endTime = urlParams.get('endTime');

                        if (startTime && endTime) {
                            currentTimeRange = {
                                preset: 'custom',
                                startTime: parseInt(startTime),
                                endTime: parseInt(endTime)
                            };
                            console.log('从URL强制设置时间范围:', currentTimeRange);
                        } else {
                            // 设置默认24小时
                            const now = moment();
                            const start = moment().subtract(24, 'hours');
                            currentTimeRange = {
                                preset: '24h',
                                startTime: start.unix(),
                                endTime: now.unix()
                            };
                            console.log('强制设置默认时间范围:', currentTimeRange);
                        }
                    }

                    // 强制调用API
                    console.log('强制调用API...');
                    loadOverviewData().catch(e => console.error('强制概览数据加载失败:', e));
                    loadDiffData().catch(e => console.error('强制差异数据加载失败:', e));
                    loadInterfaceList().catch(e => console.error('强制接口列表加载失败:', e));
                }
            }, 500);
        });
    </script>
</body>
</html>
{{end}}